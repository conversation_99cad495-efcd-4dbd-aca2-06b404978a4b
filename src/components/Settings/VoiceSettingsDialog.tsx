import { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Mic } from "lucide-react";
import { useToast } from "../../contexts/toast";

export type VoiceProvider = 'tongyi-gummy';
export type AudioListeningMode = 'microphone-only' | 'system-only' | 'dual-audio';
export type InputLayoutMode = 'bottom' | 'left';

interface TongyiGummyCredentials {
  apiKey: string;
}

// 热词项接口
interface HotWord {
  text: string; // 热词文本
  lang: string; // 语言代码，支持 'zh' 和 'en'
}

// 热词表接口
interface VocabularyTable {
  id: string; // 本地生成的唯一ID
  name: string; // 热词表名称
  vocabularyId?: string; // 阿里云返回的热词表ID
  prefix: string; // 热词表前缀
  isActive: boolean; // 是否启用
  hotWords: HotWord[]; // 热词列表
  createdAt: string; // 创建时间
  updatedAt: string; // 更新时间
}

interface VoiceConfig {
  selectedProvider: VoiceProvider;
  language: string; // 语音识别源语言
  audioListeningMode: AudioListeningMode; // 音频监听模式
  inputLayoutMode: InputLayoutMode; // 输入框布局模式
  tongyiGummy: TongyiGummyCredentials;
  vocabularyTables: VocabularyTable[]; // 热词表列表
  activeVocabularyId?: string; // 当前激活的热词表ID
  autoSend: {
    enabled: boolean; // 是否开启自动发送
    roundsToSend: number; // 每几轮识别结果自动发送（默认2轮）
    recognitionsPerRound: number; // 每轮包含多少次完整识别结果（默认3次）
  };
}

interface VoiceSettingsDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const voiceProviders = [
  {
    value: 'tongyi-gummy' as VoiceProvider,
    name: '通义Gummy语音识别',
    description: '阿里云通义模型平台实时语音识别，支持中英文混合识别和翻译',
    icon: '🤖',
    needsConfig: true
  }
];

export function VoiceSettingsDialog({ open: externalOpen, onOpenChange }: VoiceSettingsDialogProps) {
  const [open, setOpen] = useState(externalOpen || false);
  const [config, setConfig] = useState<VoiceConfig>({
    selectedProvider: 'tongyi-gummy',
    language: 'zh', // 默认中文
    audioListeningMode: 'microphone-only', // 默认仅麦克风模式
    inputLayoutMode: 'bottom', // 默认输入框在底部
    tongyiGummy: { apiKey: '' },
    vocabularyTables: [], // 默认空的热词表列表
    activeVocabularyId: undefined, // 默认没有激活的热词表
    autoSend: {
      enabled: false, // 默认关闭自动发送
      roundsToSend: 2, // 默认2轮自动发送
      recognitionsPerRound: 3 // 默认每轮3次完整识别结果
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [testingProvider, setTestingProvider] = useState<VoiceProvider | null>(null);
  const { showToast } = useToast();

  // 热词表管理相关状态
  const [vocabularyTables, setVocabularyTables] = useState<VocabularyTable[]>([]);
  const [showVocabularyDialog, setShowVocabularyDialog] = useState(false);
  const [editingVocabulary, setEditingVocabulary] = useState<VocabularyTable | null>(null);
  const [vocabularyName, setVocabularyName] = useState('');
  const [hotWords, setHotWords] = useState<HotWord[]>([]);
  const [syncingVocabulary, setSyncingVocabulary] = useState<string | null>(null);

  const dialogRef = useRef<HTMLDivElement>(null);

  // Helper function to get the correct config key
  const getProviderKey = (provider: VoiceProvider): keyof VoiceConfig => {
    switch (provider) {
      case 'tongyi-gummy':
        return 'tongyiGummy';
      default:
        return 'tongyiGummy'; // 默认返回通义Gummy
    }
  };

  // Helper function to get provider credentials
  const getProviderCredentials = (provider: VoiceProvider): TongyiGummyCredentials => {
    const key = getProviderKey(provider);
    return config[key] as TongyiGummyCredentials;
  };

  // 热词表管理方法
  const loadVocabularyTables = async () => {
    try {
      const result = await window.electronAPI.getVocabularyTables();
      if (result.success) {
        const tables = result.tables || [];
        setVocabularyTables(tables);
        // 同步更新 config 状态中的热词表数据
        setConfig(prev => ({
          ...prev,
          vocabularyTables: tables
        }));
      }
    } catch (error) {
      console.error('加载热词表失败:', error);
    }
  };

  const handleCreateVocabulary = () => {
    setEditingVocabulary(null);
    setVocabularyName('');
    setHotWords([{ text: '', lang: 'zh' }]);
    setShowVocabularyDialog(true);
  };

  const handleEditVocabulary = (vocabulary: VocabularyTable) => {
    setEditingVocabulary(vocabulary);
    setVocabularyName(vocabulary.name);
    setHotWords([...vocabulary.hotWords]);
    setShowVocabularyDialog(true);
  };

  const handleSaveVocabulary = async () => {
    if (!vocabularyName.trim()) {
      showToast('输入错误', '请输入热词表名称', 'error');
      return;
    }

    const validHotWords = hotWords.filter(word => word.text.trim());
    if (validHotWords.length === 0) {
      showToast('输入错误', '请至少添加一个热词', 'error');
      return;
    }

    try {
      setIsLoading(true);
      let result;

      if (editingVocabulary) {
        // 更新现有热词表
        result = await window.electronAPI.updateVocabularyTable(editingVocabulary.id, {
          name: vocabularyName,
          hotWords: validHotWords
        });

        // 如果热词表已同步到云端，需要同步更新
        if (result.success && editingVocabulary.vocabularyId) {
          console.log('热词表已同步到云端，正在更新云端数据...');
          const syncResult = await window.electronAPI.syncVocabularyToCloud(editingVocabulary.id);
          if (!syncResult.success) {
            showToast('警告', '本地更新成功，但云端同步失败：' + syncResult.error, 'error');
          }
        }
      } else {
        // 创建新热词表
        result = await window.electronAPI.createVocabularyTable(vocabularyName, validHotWords);
      }

      if (result.success) {
        showToast('操作成功', editingVocabulary ? '热词表更新成功' : '热词表创建成功', 'success');
        setShowVocabularyDialog(false);
        await loadVocabularyTables();
      } else {
        showToast('操作失败', result.error || '操作失败', 'error');
      }
    } catch (error) {
      console.error('保存热词表失败:', error);
      showToast('保存失败', '保存热词表时发生错误', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteVocabulary = async (vocabularyId: string) => {
    if (!confirm('确定要删除这个热词表吗？')) {
      return;
    }

    try {
      // 先检查热词表是否已同步到云端
      const table = vocabularyTables.find(t => t.id === vocabularyId);

      if (table?.vocabularyId) {
        // 如果已同步到云端，先删除云端数据
        console.log('热词表已同步到云端，正在删除云端数据...');
        const cloudDeleteResult = await window.electronAPI.deleteVocabularyFromCloud(vocabularyId);
        if (!cloudDeleteResult.success) {
          const confirmDelete = confirm(
            `云端删除失败：${cloudDeleteResult.error}\n\n是否仍要删除本地热词表？`
          );
          if (!confirmDelete) {
            return;
          }
        }
      }

      // 删除本地热词表
      const result = await window.electronAPI.deleteVocabularyTable(vocabularyId);
      if (result.success) {
        showToast('删除成功', '热词表删除成功', 'success');
        await loadVocabularyTables();
      } else {
        showToast('删除失败', result.error || '删除失败', 'error');
      }
    } catch (error) {
      console.error('删除热词表失败:', error);
      showToast('删除失败', '删除热词表时发生错误', 'error');
    }
  };

  const handleToggleVocabulary = async (vocabularyId: string, isActive: boolean) => {
    try {
      // 检查热词表是否已同步
      const table = vocabularyTables.find(t => t.id === vocabularyId);
      if (isActive && !table?.vocabularyId) {
        showToast('无法启用', '请先同步热词表到云端后再启用', 'error');
        return;
      }

      const result = await window.electronAPI.setActiveVocabularyTable(isActive ? vocabularyId : undefined);
      if (result.success) {
        await loadVocabularyTables();
        showToast('操作成功', isActive ? '热词表已启用' : '热词表已禁用', 'success');
      } else {
        showToast('操作失败', result.error || '操作失败', 'error');
      }
    } catch (error) {
      console.error('切换热词表状态失败:', error);
      showToast('操作失败', '切换热词表状态时发生错误', 'error');
    }
  };

  const handleSyncVocabulary = async (vocabularyId: string) => {
    try {
      setSyncingVocabulary(vocabularyId);
      const result = await window.electronAPI.syncVocabularyToCloud(vocabularyId);
      if (result.success) {
        showToast('同步成功', '热词表同步成功', 'success');
        await loadVocabularyTables();
      } else {
        showToast('同步失败', result.error || '同步失败', 'error');
      }
    } catch (error) {
      console.error('同步热词表失败:', error);
      showToast('同步失败', '同步热词表时发生错误', 'error');
    } finally {
      setSyncingVocabulary(null);
    }
  };

  const addHotWord = () => {
    setHotWords([...hotWords, { text: '', lang: 'zh' }]);
  };

  const updateHotWord = (index: number, field: keyof HotWord, value: string) => {
    const newHotWords = [...hotWords];
    newHotWords[index] = { ...newHotWords[index], [field]: value };
    setHotWords(newHotWords);
  };

  const removeHotWord = (index: number) => {
    if (hotWords.length > 1) {
      setHotWords(hotWords.filter((_, i) => i !== index));
    }
  };

  // Sync with external open state
  useEffect(() => {
    if (externalOpen !== undefined) {
      setOpen(externalOpen);
    }
  }, [externalOpen]);

  // Handle open state changes
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (onOpenChange && newOpen !== externalOpen) {
      onOpenChange(newOpen);
    }
  };

  // Load current config on dialog open
  useEffect(() => {
    if (open) {
      setIsLoading(true);
      Promise.all([
        window.electronAPI.getVoiceConfig(),
        loadVocabularyTables()
      ])
        .then(([voiceConfig]) => {
          setConfig(voiceConfig);
          // 同步热词表数据到本地状态
          setVocabularyTables(voiceConfig.vocabularyTables || []);
        })
        .catch((error: unknown) => {
          console.error("Failed to load voice config:", error);
          showToast("加载失败", "加载语音设置失败", "error");
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [open, showToast]);

  // Handle provider selection
  const handleProviderSelect = (provider: VoiceProvider) => {
    setConfig(prev => ({
      ...prev,
      selectedProvider: provider
    }));
  };

  // Handle credential updates
  const handleCredentialUpdate = (provider: VoiceProvider, field: string, value: string) => {
    setConfig(prev => {
      const providerKey = getProviderKey(provider);
      const currentCredentials = prev[providerKey];

      // 只处理通义Gummy的凭据更新
      const tongyiCreds = currentCredentials as TongyiGummyCredentials;
      return {
        ...prev,
        [providerKey]: {
          ...tongyiCreds,
          [field]: value
        }
      };
    });
  };

  // Test credentials
  const handleTestCredentials = async (provider: VoiceProvider) => {
    setTestingProvider(provider);
    try {
      const credentials = getProviderCredentials(provider);
      const result = await window.electronAPI.testVoiceCredentials(provider, credentials);
      
      if (result.valid) {
        showToast("成功", "API凭据验证成功", "success");
      } else {
        showToast("错误", result.error || "API凭据验证失败", "error");
      }
    } catch (error) {
      console.error("Failed to test credentials:", error);
      showToast("错误", "测试API凭据时出错", "error");
    } finally {
      setTestingProvider(null);
    }
  };

  // Save configuration
  const handleSave = async () => {
    setIsLoading(true);
    try {
      // 创建配置副本，排除热词表数据（热词表通过专门的API管理）
      const configToSave = {
        selectedProvider: config.selectedProvider,
        language: config.language,
        audioListeningMode: config.audioListeningMode,
        inputLayoutMode: config.inputLayoutMode,
        tongyiGummy: config.tongyiGummy,
        activeVocabularyId: config.activeVocabularyId,
        autoSend: config.autoSend
        // 注意：不包含 vocabularyTables，因为它们通过专门的热词表API管理
      };

      const result = await window.electronAPI.updateVoiceConfig(configToSave);

      if (result) {
        showToast("成功", "语音设置保存成功", "success");
        handleOpenChange(false);
      }
    } catch (error) {
      console.error("Failed to save voice settings:", error);
      showToast("错误", "保存语音设置失败", "error");
    } finally {
      setIsLoading(false);
    }
  };

  // Open external link handler
  const openExternalLink = (url: string) => {
    window.electronAPI.openLink(url);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent
        className="sm:max-w-lg"
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 'min(500px, 90vw)',
          maxHeight: '90vh',
          overflowY: 'auto',
          zIndex: 9999,
          margin: 0,
          padding: '20px',
          transition: 'all 0.25s ease',
          opacity: 0.98,
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-primary)',
          color: 'var(--text-primary)',
        }}
        ref={dialogRef}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2" style={{ color: 'var(--text-primary)' }}>
            <Mic className="h-5 w-5" />
            语音识别 API 设置
          </DialogTitle>
          <DialogDescription style={{ color: 'var(--text-tertiary)' }}>
            配置您的语音识别服务。选择适合您需求的语音识别提供商。
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          {/* Provider Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>选择语音识别服务</label>
            <div className="space-y-2">
              {voiceProviders.map((provider) => (
                <div
                  key={provider.value}
                  className="p-3 rounded-lg cursor-pointer transition-colors"
                  style={{
                    backgroundColor: config.selectedProvider === provider.value ? 'var(--bg-tertiary)' : 'var(--bg-secondary)',
                    border: `1px solid ${config.selectedProvider === provider.value ? 'var(--border-primary)' : 'var(--border-secondary)'}`,
                  }}
                  onClick={() => handleProviderSelect(provider.value)}
                  onMouseEnter={(e) => {
                    if (config.selectedProvider !== provider.value) {
                      e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (config.selectedProvider !== provider.value) {
                      e.currentTarget.style.backgroundColor = 'var(--bg-secondary)';
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full border-2"
                      style={{
                        backgroundColor: config.selectedProvider === provider.value ? 'var(--text-primary)' : 'transparent',
                        borderColor: config.selectedProvider === provider.value ? 'var(--text-primary)' : 'var(--text-tertiary)',
                      }}
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{provider.icon}</span>
                        <p className="font-medium text-sm" style={{ color: 'var(--text-primary)' }}>{provider.name}</p>
                      </div>
                      <p className="text-xs mt-1" style={{ color: 'var(--text-tertiary)' }}>{provider.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Configuration for selected provider */}
          <div className="space-y-4">
            <div className="pt-4" style={{ borderTop: '1px solid var(--border-primary)' }}>
              <h3 className="text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                {voiceProviders.find(p => p.value === config.selectedProvider)?.name} 配置
              </h3>

              <div className="space-y-3">
                {/* 语音识别语言选择 */}
                <div>
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>识别语言</label>
                  <select
                    value={config.language}
                    onChange={(e) => setConfig(prev => ({ ...prev, language: e.target.value }))}
                    className="w-full text-sm rounded-md px-3 py-2 focus:outline-none"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-primary)';
                    }}
                  >
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                  </select>
                  <p className="text-xs mt-1" style={{ color: 'var(--text-tertiary)' }}>选择语音识别的源语言</p>
                </div>

                {/* 音频监听模式选择 */}
                <div>
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>音频监听模式</label>
                  <select
                    value={config.audioListeningMode}
                    onChange={(e) => setConfig(prev => ({ ...prev, audioListeningMode: e.target.value as AudioListeningMode }))}
                    className="w-full text-sm rounded-md px-3 py-2 focus:outline-none"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-primary)';
                    }}
                  >
                    <option value="microphone-only">🎤 仅麦克风</option>
                    <option value="system-only">🔊 仅系统音频</option>
                    <option value="dual-audio">🎧 双音频源</option>
                  </select>
                  <p className="text-xs mt-1" style={{ color: 'var(--text-tertiary)' }}>
                    选择语音输入源：仅麦克风（用户语音）、仅系统音频（面试官语音）或双音频源（同时监听）
                  </p>
                </div>

                {/* 输入框布局模式选择 */}
                <div>
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>输入框布局</label>
                  <select
                    value={config.inputLayoutMode}
                    onChange={(e) => setConfig(prev => ({ ...prev, inputLayoutMode: e.target.value as InputLayoutMode }))}
                    className="w-full text-sm rounded-md px-3 py-2 focus:outline-none"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-primary)';
                    }}
                  >
                    <option value="bottom">📱 底部布局</option>
                    <option value="left">📋 左侧布局</option>
                  </select>
                  <p className="text-xs mt-1" style={{ color: 'var(--text-tertiary)' }}>
                    选择输入框位置：底部布局（传统模式）或左侧布局（适合长文本显示）
                  </p>
                </div>

                {/* 自动发送配置 */}
                <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                  <div className="flex items-center justify-between mb-3">
                    <label className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>自动发送消息</label>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.autoSend.enabled}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          autoSend: { ...prev.autoSend, enabled: e.target.checked }
                        }))}
                        className="w-4 h-4 mr-2"
                        style={{ accentColor: 'var(--accent-primary)' }}
                      />
                      <span className="text-xs" style={{ color: 'var(--text-primary)' }}>
                        {config.autoSend.enabled ? '已开启' : '已关闭'}
                      </span>
                    </div>
                  </div>

                  <p className="text-xs mb-3" style={{ color: 'var(--text-tertiary)' }}>
                    开启后，语音识别达到设定轮次时将自动发送消息给AI助手。手动发送仍然可以正常使用。
                  </p>

                  {config.autoSend.enabled && (
                    <div className="space-y-3">
                      <div>
                        <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>
                          自动发送轮次（每{config.autoSend.recognitionsPerRound}次完整识别算1轮）
                        </label>
                        <select
                          value={config.autoSend.roundsToSend}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            autoSend: { ...prev.autoSend, roundsToSend: parseInt(e.target.value) }
                          }))}
                          className="w-full text-sm rounded-md px-3 py-2 focus:outline-none"
                          style={{
                            backgroundColor: 'var(--bg-primary)',
                            border: '1px solid var(--border-primary)',
                            color: 'var(--text-primary)',
                          }}
                        >
                          <option value={1}>1轮后自动发送</option>
                          <option value={2}>2轮后自动发送</option>
                          <option value={3}>3轮后自动发送</option>
                          <option value={4}>4轮后自动发送</option>
                          <option value={5}>5轮后自动发送</option>
                        </select>
                      </div>

                      <div>
                        <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>
                          每轮识别次数
                        </label>
                        <select
                          value={config.autoSend.recognitionsPerRound}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            autoSend: { ...prev.autoSend, recognitionsPerRound: parseInt(e.target.value) }
                          }))}
                          className="w-full text-sm rounded-md px-3 py-2 focus:outline-none"
                          style={{
                            backgroundColor: 'var(--bg-primary)',
                            border: '1px solid var(--border-primary)',
                            color: 'var(--text-primary)',
                          }}
                        >
                          <option value={2}>每2次完整识别算1轮</option>
                          <option value={3}>每3次完整识别算1轮</option>
                          <option value={4}>每4次完整识别算1轮</option>
                          <option value={5}>每5次完整识别算1轮</option>
                          <option value={6}>每6次完整识别算1轮</option>
                        </select>
                      </div>

                      <div className="p-2 rounded" style={{ backgroundColor: 'var(--bg-tertiary)', border: '1px solid var(--border-secondary)' }}>
                        <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                          当前设置：每{config.autoSend.recognitionsPerRound}次完整语音识别为1轮，
                          {config.autoSend.roundsToSend}轮后（即{config.autoSend.roundsToSend * config.autoSend.recognitionsPerRound}次完整识别后）自动发送消息
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 通义Gummy只需要API Key */}
                <div>
                  <label className="text-xs mb-1 block" style={{ color: 'var(--text-tertiary)' }}>API Key</label>
                  <Input
                    type="password"
                    value={getProviderCredentials(config.selectedProvider).apiKey}
                    onChange={(e) => handleCredentialUpdate(config.selectedProvider, 'apiKey', e.target.value)}
                    placeholder="请输入通义模型平台的 API Key"
                    className="text-sm"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)',
                    }}
                  />
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestCredentials(config.selectedProvider)}
                  disabled={testingProvider === config.selectedProvider}
                  className="text-xs"
                  style={{
                    border: '1px solid var(--border-primary)',
                    backgroundColor: 'transparent',
                    color: 'var(--text-primary)',
                  }}
                  onMouseEnter={(e) => {
                    if (testingProvider !== config.selectedProvider) {
                      e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (testingProvider !== config.selectedProvider) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                >
                  {testingProvider === config.selectedProvider ? "测试中..." : "测试连接"}
                </Button>
              </div>

              {/* Help text */}
              <div className="mt-4 space-y-3">
                <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                  <p className="text-xs mb-2" style={{ color: 'var(--text-secondary)' }}>获取API凭据：</p>
                  <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>
                    1. 访问 <button
                      onClick={() => openExternalLink('https://bailian.console.aliyun.com/')}
                      className="hover:underline cursor-pointer"
                      style={{ color: 'var(--accent-primary)' }}
                    >阿里云百炼平台</button>
                  </p>
                  <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>2. 开通模型服务</p>
                  <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>3. 获取API Key</p>
                  <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>4. 确保已开通Gummy实时语音识别服务</p>
                </div>

                <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                  <p className="text-xs mb-2" style={{ color: 'var(--text-secondary)' }}>音频监听模式说明：</p>
                  <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>
                    • <strong>仅麦克风</strong>：只监听用户的麦克风输入，适合单人语音输入场景
                  </p>
                  <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>
                    • <strong>仅系统音频</strong>：只监听系统音频输出，适合监听面试官语音
                  </p>
                  <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                    • <strong>双音频源</strong>：同时监听麦克风和系统音频，自动区分"我"和"面试官"（推荐带耳机使用，效果最佳，外放会有回音）
                  </p>
                </div>

                {/* 热词表管理 */}
                <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>定制热词表（可选）</p>
                    <Button
                      onClick={handleCreateVocabulary}
                      size="sm"
                      className="text-xs px-2 py-1"
                      style={{
                        backgroundColor: 'var(--accent-primary)',
                        color: 'white',
                        border: 'none'
                      }}
                    >
                      新建热词表
                    </Button>
                  </div>

                  <p className="text-xs mb-3" style={{ color: 'var(--text-tertiary)' }}>
                    通过设置热词表可以提高特定词汇的语音识别准确率，只能启用一个热词表。
                    <br />
                    <span style={{ color: 'var(--text-warning)' }}>
                      注意：热词表需要先同步到云端才能在语音识别中使用。
                    </span>
                  </p>

                  {vocabularyTables.length === 0 ? (
                    <p className="text-xs text-center py-4" style={{ color: 'var(--text-tertiary)' }}>
                      暂无热词表，点击"新建热词表"开始创建
                    </p>
                  ) : (
                    <div className="space-y-2">
                      {vocabularyTables.map((table) => (
                        <div
                          key={table.id}
                          className="flex items-center justify-between p-2 rounded border"
                          style={{
                            backgroundColor: table.isActive ? 'var(--accent-primary-10)' : 'var(--bg-primary)',
                            border: `1px solid ${table.isActive ? 'var(--accent-primary)' : 'var(--border-primary)'}`
                          }}
                        >
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={table.isActive}
                              disabled={!table.vocabularyId} // 未同步的热词表不能被勾选
                              onChange={(e) => handleToggleVocabulary(table.id, e.target.checked)}
                              className="w-3 h-3"
                              style={{
                                opacity: table.vocabularyId ? 1 : 0.5,
                                cursor: table.vocabularyId ? 'pointer' : 'not-allowed'
                              }}
                            />
                            <div>
                              <p className="text-xs font-medium" style={{
                                color: table.vocabularyId ? 'var(--text-primary)' : 'var(--text-secondary)'
                              }}>
                                {table.name}
                                {!table.vocabularyId && (
                                  <span className="text-xs ml-2 px-1 py-0.5 rounded" style={{
                                    backgroundColor: 'var(--text-warning-10)',
                                    color: 'var(--text-warning)',
                                    fontSize: '10px'
                                  }}>
                                    需同步
                                  </span>
                                )}
                              </p>
                              <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
                                {table.hotWords.length} 个热词
                                {table.vocabularyId ? (
                                  <span style={{ color: 'var(--accent-primary)' }}> • 已同步到云端，可用于语音识别</span>
                                ) : (
                                  <span style={{ color: 'var(--text-warning)' }}> • 仅本地存储，需同步后才能使用</span>
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Button
                              onClick={() => handleSyncVocabulary(table.id)}
                              disabled={syncingVocabulary === table.id}
                              size="sm"
                              className="text-xs px-2 py-1"
                              style={{
                                backgroundColor: table.vocabularyId ? 'var(--accent-tertiary)' : 'var(--accent-secondary)',
                                color: 'white',
                                border: 'none'
                              }}
                            >
                              {syncingVocabulary === table.id ? '同步中...' : (table.vocabularyId ? '重新同步' : '同步')}
                            </Button>
                            <Button
                              onClick={() => handleEditVocabulary(table)}
                              size="sm"
                              variant="outline"
                              className="text-xs px-2 py-1"
                              style={{
                                border: '1px solid var(--border-primary)',
                                backgroundColor: 'transparent',
                                color: 'var(--text-primary)'
                              }}
                            >
                              编辑
                            </Button>
                            <Button
                              onClick={() => handleDeleteVocabulary(table.id)}
                              size="sm"
                              variant="outline"
                              className="text-xs px-2 py-1"
                              style={{
                                border: '1px solid var(--border-danger)',
                                backgroundColor: 'transparent',
                                color: 'var(--text-danger)'
                              }}
                            >
                              删除
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            style={{
              border: '1px solid var(--border-primary)',
              backgroundColor: 'transparent',
              color: 'var(--text-primary)',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            取消
          </Button>
          <Button
            className="px-4 py-3 rounded-xl font-medium transition-colors"
            onClick={handleSave}
            disabled={isLoading}
            style={{
              backgroundColor: 'var(--text-primary)',
              color: 'var(--bg-primary)',
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.currentTarget.style.opacity = '0.9';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading) {
                e.currentTarget.style.opacity = '1';
              }
            }}
          >
            {isLoading ? "保存中..." : "保存设置"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* 热词表编辑对话框 */}
    <Dialog open={showVocabularyDialog} onOpenChange={setShowVocabularyDialog}>
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay
          className="fixed inset-0 bg-black bg-opacity-50"
          style={{ zIndex: 10000 }}
        />
        <DialogPrimitive.Content
          className="max-w-2xl max-h-[80vh] overflow-y-auto"
          style={{
            zIndex: 10001,
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-primary)',
            color: 'var(--text-primary)',
            borderRadius: '8px',
            padding: '20px',
            width: 'min(800px, 90vw)',
            maxHeight: '80vh',
            boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
          }}
        >
        <DialogHeader>
          <DialogTitle>
            {editingVocabulary ? '编辑热词表' : '新建热词表'}
            {editingVocabulary?.vocabularyId && (
              <span className="text-xs ml-2 px-2 py-1 rounded" style={{
                backgroundColor: 'var(--accent-primary-10)',
                color: 'var(--accent-primary)'
              }}>
                已同步到云端
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            设置热词可以提高特定词汇的语音识别准确率
            {editingVocabulary?.vocabularyId && (
              <span className="block mt-1 text-xs" style={{ color: 'var(--text-warning)' }}>
                注意：修改后将自动同步到云端
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 热词表名称 */}
          <div>
            <label className="text-sm font-medium mb-2 block" style={{ color: 'var(--text-primary)' }}>
              热词表名称
            </label>
            <Input
              value={vocabularyName}
              onChange={(e) => setVocabularyName(e.target.value)}
              placeholder="请输入热词表名称"
              className="w-full"
            />
          </div>

          {/* 热词列表 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                热词列表
              </label>
              <Button
                onClick={addHotWord}
                size="sm"
                className="text-xs px-2 py-1"
                style={{
                  backgroundColor: 'var(--accent-primary)',
                  color: 'white',
                  border: 'none'
                }}
              >
                添加热词
              </Button>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {hotWords.map((word, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    value={word.text}
                    onChange={(e) => updateHotWord(index, 'text', e.target.value)}
                    placeholder="请输入热词文本"
                    className="flex-1"
                  />
                  <select
                    value={word.lang}
                    onChange={(e) => updateHotWord(index, 'lang', e.target.value)}
                    className="px-2 py-1 border rounded text-sm"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      border: '1px solid var(--border-primary)',
                      color: 'var(--text-primary)'
                    }}
                  >
                    <option value="zh">中文</option>
                    <option value="en">英文</option>
                  </select>
                  {hotWords.length > 1 && (
                    <Button
                      onClick={() => removeHotWord(index)}
                      size="sm"
                      variant="outline"
                      className="text-xs px-2 py-1"
                      style={{
                        border: '1px solid var(--border-danger)',
                        backgroundColor: 'transparent',
                        color: 'var(--text-danger)'
                      }}
                    >
                      删除
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 说明文字 */}
          <div className="p-3 rounded-md" style={{ backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-primary)' }}>
            <p className="text-xs mb-2" style={{ color: 'var(--text-secondary)' }}>热词设置说明：</p>
            <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>
              • 热词文本应使用实际词语，避免任意字符组合
            </p>
            <p className="text-xs mb-1" style={{ color: 'var(--text-tertiary)' }}>
              • 中文热词长度不超过15个字符，英文热词不超过7个单词
            </p>
            <p className="text-xs" style={{ color: 'var(--text-tertiary)' }}>
              • 每个热词表最多支持500个热词
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setShowVocabularyDialog(false)}
            style={{
              border: '1px solid var(--border-primary)',
              backgroundColor: 'transparent',
              color: 'var(--text-primary)',
            }}
          >
            取消
          </Button>
          <Button
            onClick={handleSaveVocabulary}
            disabled={isLoading}
            style={{
              backgroundColor: 'var(--text-primary)',
              color: 'var(--bg-primary)',
            }}
          >
            {isLoading ? "保存中..." : "保存"}
          </Button>
        </DialogFooter>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </Dialog>
    </>
  );
}
